<div>
  <style>
      me {
        position: relative;
        height: 5.9375rem;
        margin-top: 1rem;
        margin-bottom: 1rem;
        flex-basis: 100%;
      }

      me input {
        height: 2rem;
        position: absolute;
        top: 1.875rem;
        left: 0;
        right: 0;
        font-family: 'Noto Sans', sans-serif;
        font-size: 1.125rem;
        color: var(--color-text-black);
        border: 0;
        z-index: 1;
        background-color: transparent;
        border-bottom: 1px solid var(--color-input-lines);
        padding: 0;
        border-radius: 0;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;

        &:focus {
            outline: 0;
            border-bottom: 1px solid var(--color-input-lines);

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 0.9375rem;
                color: var(--color-text-dark);
                top: 0.625rem;
                right: 2rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        &:not(:placeholder-shown):valid {
            border-bottom: 1px solid var(--color-selected-green);

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 0.9375rem;
                color: var(--color-text-dark);
                top: 0.625rem;
                right: 2rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        &:not(:placeholder-shown):invalid {
            border-bottom: 1px solid var(--color-selected-red);

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 0.9375rem;
                color: var(--color-text-dark);
                top: 0.625rem;
                right: 2rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        &:not(:placeholder-shown):not(:focus):invalid~.input-group__error {
            display: block;
        }

        /* Hide error on page load for fields with default values */
        &[data-touched="false"]:not(:focus)~.input-group__error {
            display: none;
        }

        &:disabled {
            color: var(--color-disabled);
            border-bottom: 1px solid var(--color-disabled);
        }
        &:disabled + .input-label {
            color: var(--color-disabled);
        }
        &:disabled + .input-label.float-label {
            color: var(--color-disabled);
        }
      }

      me .input-group__error {
          position: absolute;
          top: 4.375rem;
          left: 0.625rem;
          color: #bc2600;
          display: none;
          font-family: 'Noto Serif', serif;
          font-size: 0.875rem;
          line-height: 1.2;
          max-width: calc(100% - 1.25rem);
          word-wrap: break-word;
      }

  </style>

  <input pattern="{{ pattern | default('') }}" placeholder=" " type="{{ type | default('text') }}" {% if value %}value="{{ value }}" data-touched="false"{% else %}data-touched="true"{% endif %} name="{{ namealwayschange }}" id="{{ namealwayschange }}" {% if required is not defined or required %} required{% endif %} onblur="this.setAttribute('data-touched', 'true')" oninput="this.setAttribute('data-touched', 'true')" />
  <label class="input-label">
    <style>
        me {
            position: absolute;
            top: 2.25rem;
            left: 0;
            right: 2rem;
            color: var(--color-text-black);
            transition: .15s ease;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .input-label:disabled, input:disabled + .input-label {
            var(--color-disabled);
        }
        input:disabled + .input-label.float-label {
            var(--color-disabled);
        }
    </style>
    {{ label | safe }}
  </label>

  <span class="input-group__error">{{ errormessage }}</span>
</div>


{# Here are the different input types you can use in HTML:

<input type="button">
<input type="checkbox">
<input type="color">
<input type="date">
<input type="datetime-local">
<input type="email">
<input type="file">
<input type="hidden">
<input type="image">
<input type="month">
<input type="number">
<input type="password">
<input type="radio">
<input type="range">
<input type="reset">
<input type="search">
<input type="submit">
<input type="tel">
<input type="text">
<input type="time">
<input type="url">
<input type="week">

#}


{# The value attribute specifies the value of an <input> element.
The value attribute is used differently for different input types:
For "button", "reset", and "submit" - it defines the text on the button
For "text", "password", and "hidden" - it defines the initial (default) value of the input field
For "checkbox", "radio", "image" - it defines the value associated with the input (this is also the value that is sent on submit) #}